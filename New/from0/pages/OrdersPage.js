import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Download,
  FileText,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  RefreshCw,
  ShoppingCart,
  Edit,
  Trash2,
  File,
  AlertCircle
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import NewOrderModal from '../components/NewOrderModal';
import InvoiceModal from '../components/InvoiceModal';
import InvoiceView from '../components/InvoiceView';
import ServiceDetailModal from '../components/ServiceDetailModal';
import { API_URL } from '../config';
import UpgradeConfigurationDisplay from '../components/UpgradeConfigurationDisplay';
const OrdersPage = ({ navigateTo, toggleSidebar, sidebarCollapsed }) => {
  // State for data
  const [orders, setOrders] = useState([]);
  const [allOrders, setAllOrders] = useState([]);
  const [upgradeOrders, setUpgradeOrders] = useState([]);
  const [allUpgradeOrders, setAllUpgradeOrders] = useState([]);
  const [orderStats, setOrderStats] = useState([]);

  // State for filters and sorting
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedLocation, setSelectedLocation] = useState('All Locations');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('All');
  const [sortField, setSortField] = useState('date');
  const [sortDirection, setSortDirection] = useState('desc');

  // State for tab management
  const [activeTab, setActiveTab] = useState('orders'); // 'orders' or 'upgrades'

  // State for UI
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('Today at 15:32');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for location and payment status options
  const [uniqueLocations, setUniqueLocations] = useState(['All Locations']);
  const [uniquePaymentStatuses, setUniquePaymentStatuses] = useState(['All']);

  // State for modals
  const [newOrderModalOpen, setNewOrderModalOpen] = useState(false);
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [viewingInvoice, setViewingInvoice] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editModalMode, setEditModalMode] = useState('view');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [serviceModalOpen, setServiceModalOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState(null);

  // State for server configuration options (important for enhancing service data)
  const [serverOptions, setServerOptions] = useState({
    cpus: [],
    bandwidths: [],
    storages: [],
    configurations: [],
    cities: []
  });

  // API base URL and endpoints

  const API_ENDPOINTS = {
    SERVER_DETAILS: `${API_URL}/api_admin_inventory.php?f=get_inventory_dedicated`,
    SERVER_OPTIONS: `${API_URL}/api_admin_server_config.php?f=get_server_options`,
    CITIES: `${API_URL}/api_admin_inventory.php?f=get_cities`,
    CPU_DETAILS: `${API_URL}/api_admin_server_config.php?f=get_cpu_details`,
    STORAGE_DETAILS: `${API_URL}/api_admin_server_config.php?f=get_storage_details`,
    BANDWIDTH_DETAILS: `${API_URL}/api_admin_server_config.php?f=get_bandwidth_details`,
    CITY_DETAILS: `${API_URL}/api_admin_inventory.php?f=get_city_details`,
  };

  // Load initial data
  useEffect(() => {
    loadAllData();
    // Also load server configuration options for service enhancements
    fetchServerOptions();
  }, []);

  // Ensure sidebar is closed on mobile when page loads - only run once
  useEffect(() => {
    // Check if we're on mobile
    const isMobile = window.innerWidth <= 800;

    // If on mobile and sidebar is open, close it
    if (isMobile && !sidebarCollapsed && typeof toggleSidebar === 'function') {
      console.log("OrdersPage: Closing sidebar on mobile");
      // Use a small timeout to ensure this happens after any other sidebar operations
      setTimeout(() => {
        toggleSidebar();
      }, 100);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to load all data
  const loadAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchOrders(),
        fetchUpgradeOrders(),
        fetchOrderStats()
      ]);

      setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch orders
  const fetchOrders = async () => {
    try {
      // Prepare filters for the API request
      const filters = {
        status: selectedStatus === 'All' ? '' : selectedStatus,
        location: selectedLocation === 'All Locations' ? '' : selectedLocation,
        paymentStatus: selectedPaymentStatus === 'All' ? '' : selectedPaymentStatus,
        search: searchQuery,
        sortField: sortField,
        sortDirection: sortDirection
      };

      const response = await fetch(`${API_URL}/api_admin_orders.php?f=get_orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ...filters
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // Update orders state only if we got data
      if (Array.isArray(data) && data.length > 0) {
        setOrders(data);
        setAllOrders(data);

        // Extract unique locations and payment statuses for filters
        const locations = ['All Locations', ...new Set(data.map(order => order.location))];
        const paymentStatuses = ['All', ...new Set(data.map(order => order.paymentStatus))];

        setUniqueLocations(locations);
        setUniquePaymentStatuses(paymentStatuses);
      } else if (orders.length === 0) {
        // If we got no data and have no existing orders, initialize with empty arrays
        setOrders([]);
        setAllOrders([]);
        setUniqueLocations(['All Locations']);
        setUniquePaymentStatuses(['All']);
      }

      return true; // Indicate successful fetch

    } catch (err) {
      console.error("Error fetching orders:", err);
      // Don't modify the state if there was an error and we already have orders
      return false; // Indicate fetch failed
    }
  };

  // Function to fetch upgrade orders
  const fetchUpgradeOrders = async () => {
    try {
      // Prepare filters for the API request
      const filters = {
        status: selectedStatus === 'All' ? '' : selectedStatus,
        search: searchQuery,
        sortField: sortField,
        sortDirection: sortDirection
      };

      const response = await fetch(`${API_URL}/api_admin_orders.php?f=get_upgrade_orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token'),
          ...filters
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // Update upgrade orders state
      if (Array.isArray(data)) {
        setUpgradeOrders(data);
        setAllUpgradeOrders(data);
      } else {
        setUpgradeOrders([]);
        setAllUpgradeOrders([]);
      }

      return true; // Indicate successful fetch

    } catch (err) {
      console.error("Error fetching upgrade orders:", err);
      // Don't modify the state if there was an error
      return false; // Indicate fetch failed
    }
  };

  // Function to fetch order statistics
  const fetchOrderStats = async () => {
    try {
      const response = await fetch(`${API_URL}/api_admin_orders.php?f=get_order_stats`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();
      setOrderStats(data);

    } catch (err) {
      console.error("Error fetching order stats:", err);

      // Set fallback stats for demo if fetch fails
      setOrderStats([
        {
          title: 'Total Orders',
          value: '583',
          change: '+12.5%',
          period: 'vs last month',
          icon_class: 'icon-dropshadow-info'
        },
        {
          title: 'Completed',
          value: '429',
          change: '****%',
          period: 'vs last month',
          icon_class: 'icon-dropshadow-success'
        },
        {
          title: 'Pending',
          value: '85',
          change: '-3.1%',
          period: 'vs last month',
          icon_class: 'icon-dropshadow-warning'
        },
        {
          title: 'Cancelled',
          value: '24',
          change: '****%',
          period: 'vs last month',
          icon_class: 'icon-dropshadow-danger'
        }
      ]);
    }
  };

  // Function to fetch server options (CPUs, storage, bandwidth, locations)
  // This is crucial for enhancing service data with proper labels
  const fetchServerOptions = async () => {
    try {
      console.log('Fetching server configuration options...');

      // Fetch server options
      const response = await fetch(API_ENDPOINTS.SERVER_OPTIONS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status} fetching server options`);
      }

      // Get the raw response text for debugging
      const responseText = await response.text();
      console.log('Raw server options response:', responseText);

      // Parse response safely
      let data;
      try {
        data = JSON.parse(responseText);
        console.log('Fetched server options:', data);
      } catch (parseError) {
        console.error('Error parsing server options JSON:', parseError);
        throw new Error('Invalid JSON in server options response');
      }

      // Check if we have valid options
      if (data.success && data.options) {
        // Create options structure
        const newServerOptions = {
          cpus: Array.isArray(data.options.cpus) ? data.options.cpus : [],
          bandwidths: Array.isArray(data.options.bandwidths) ? data.options.bandwidths : [],
          storages: Array.isArray(data.options.storages) ? data.options.storages : [],
          configurations: Array.isArray(data.options.configurations) ? data.options.configurations : [],
          cities: Array.isArray(data.options.cities) ? data.options.cities : [],
        };

        // Make sure we have at least some data for each option
        if (newServerOptions.cpus.length === 0) {
          newServerOptions.cpus = [
            { id: 1, cpu: "Intel Xeon E5-2680v2" },
            { id: 2, cpu: "AMD EPYC 7302" }
          ];
        }

        if (newServerOptions.bandwidths.length === 0) {
          newServerOptions.bandwidths = [
            { id: 1, name: "1Gbps Unmetered", description: "1Gbps Unmetered" },
            { id: 2, name: "10Gbps Unmetered", description: "10Gbps Unmetered" },
            { id: 5, name: "20Gbps Unmetered", description: "20Gbps Unmetered" }
          ];
        }

        if (newServerOptions.storages.length === 0) {
          newServerOptions.storages = [
            { id: 1, name: "1TB SSD", description: "1TB SSD" },
            { id: 2, name: "2TB SSD", description: "2TB SSD" }
          ];
        }

        setServerOptions(newServerOptions);

        // If we have cities but they're empty, fetch them separately
        if (newServerOptions.cities.length === 0) {
          await fetchCities();
        }

      } else {
        // Create default options
        const defaultOptions = {
          cpus: [
            { id: 1, cpu: "Intel Xeon E5-2680v2" },
            { id: 2, cpu: "AMD EPYC 7302" }
          ],
          bandwidths: [
            { id: 1, name: "1Gbps Unmetered", description: "1Gbps Unmetered" },
            { id: 2, name: "10Gbps Unmetered", description: "10Gbps Unmetered" },
            { id: 5, name: "20Gbps Unmetered", description: "20Gbps Unmetered" }
          ],
          storages: [
            { id: 1, name: "1TB SSD", description: "1TB SSD" },
            { id: 2, name: "2TB SSD", description: "2TB SSD" }
          ],
          configurations: [],
          cities: []
        };

        setServerOptions(defaultOptions);

        // If we don't have cities, fetch them separately
        await fetchCities();
      }
    } catch (err) {
      console.error("Error fetching server options:", err);

      // Create default options
      const fallbackOptions = {
        cpus: [
          { id: 1, cpu: "Intel Xeon E5-2680v2" },
          { id: 2, cpu: "AMD EPYC 7302" }
        ],
        bandwidths: [
          { id: 1, name: "1Gbps Unmetered", description: "1Gbps Unmetered" },
          { id: 2, name: "10Gbps Unmetered", description: "10Gbps Unmetered" },
          { id: 5, name: "20Gbps Unmetered", description: "20Gbps Unmetered" }
        ],
        storages: [
          { id: 1, name: "1TB SSD", description: "1TB SSD" },
          { id: 2, name: "2TB SSD", description: "2TB SSD" }
        ],
        configurations: [],
        cities: []
      };

      setServerOptions(fallbackOptions);

      // Try to fetch cities separately
      await fetchCities();
    }
  };

  // Function to fetch cities
  const fetchCities = async () => {
    try {
      console.log('Fetching cities...');

      const response = await fetch(API_ENDPOINTS.CITIES, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      // If we got cities, update server options
      if (Array.isArray(data) && data.length > 0) {
        setServerOptions(prev => ({
          ...prev,
          cities: data.map(city => ({
            id: city.id,
            name: city.city,
            city: city.city,
            country: city.country_name || 'Unknown'
          }))
        }));
      } else {
        // Set default cities
        const defaultCities = [
          { id: 1, name: "Bucharest", city: "Bucharest", country: "Romania" },
          { id: 2, name: "Frankfurt", city: "Frankfurt", country: "Germany" }
        ];

        setServerOptions(prev => ({
          ...prev,
          cities: defaultCities
        }));
      }
    } catch (error) {
      console.error('Error fetching cities:', error);

      // Set default cities on error
      const defaultCities = [
        { id: 1, name: "Bucharest", city: "Bucharest", country: "Romania" },
        { id: 2, name: "Frankfurt", city: "Frankfurt", country: "Germany" }
      ];

      setServerOptions(prev => ({
        ...prev,
        cities: defaultCities
      }));
    }
  };

  // Helper function to enhance service data with descriptive information
  const enhanceServiceData = (serviceData) => {
    if (!serviceData) return null;

    // Create a copy of the service data
    const enhancedService = { ...serviceData };

    // Add CPU name if we have the ID
    if (serviceData.cpu_id && !serviceData.cpu && serverOptions.cpus.length > 0) {
      const cpuOption = serverOptions.cpus.find(cpu => cpu.id == serviceData.cpu_id);
      if (cpuOption) {
        enhancedService.cpu = cpuOption.cpu || `CPU #${serviceData.cpu_id}`;
      }
    }

    // Add Storage name if we have the ID
    if (serviceData.storage_id && !serviceData.storage && serverOptions.storages.length > 0) {
      const storageOption = serverOptions.storages.find(storage => storage.id == serviceData.storage_id);
      if (storageOption) {
        enhancedService.storage = storageOption.name || storageOption.description || `Storage #${serviceData.storage_id}`;
      }
    }

    // Add Bandwidth name if we have the ID
    if (serviceData.bandwidth_id && !serviceData.bandwidth && serverOptions.bandwidths.length > 0) {
      const bandwidthOption = serverOptions.bandwidths.find(bandwidth => bandwidth.id == serviceData.bandwidth_id);
      if (bandwidthOption) {
        enhancedService.bandwidth = bandwidthOption.name || bandwidthOption.description || `Bandwidth #${serviceData.bandwidth_id}`;
      }
    }

    // Add Location name if we have the ID
    if (serviceData.location_id && !serviceData.location && serverOptions.cities.length > 0) {
      const locationOption = serverOptions.cities.find(city => city.id == serviceData.location_id);
      if (locationOption) {
        enhancedService.location = locationOption.name || locationOption.city || `Location #${serviceData.location_id}`;
        if (locationOption.country) {
          enhancedService.location += `, ${locationOption.country}`;
        }
      }
    }

    return enhancedService;
  };

  // Function to search clients for the new order modal
  const handleClientSearch = async (query) => {
    if (!query || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch(`${API_URL}/api_admin_invoices.php?f=search_users&q=${encodeURIComponent(query)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: localStorage.getItem('admin_token')
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setSearchResults(data);
      } else {
        setSearchResults([]);
      }
    } catch (err) {
      console.error("Error searching clients:", err);
      // Set some fallback results for demo
      setSearchResults([
        { id: 1, first_name: 'John', last_name: 'Doe', company_name: 'Acme Corp', email: '<EMAIL>' },
        { id: 2, first_name: 'Jane', last_name: 'Smith', company_name: 'TechCorp', email: '<EMAIL>' }
      ]);
    } finally {
      setIsSearching(false);
    }
  };

  // Function to create a new order
  const handleCreateOrder = async (orderData) => {
    try {
      setLoading(true);

      console.log('Received order data from modal:', orderData);

      // Close modal
      setNewOrderModalOpen(false);

      // Add new order to state
      if (orderData && orderData.success) {
        // Format the order data to match the expected format
        const formattedOrder = {
          id: orderData.id || `#ORD-${new Date().getTime()}`,
          customerName: orderData.customerName,
          date: orderData.date || new Date().toISOString().split('T')[0],
          amount: orderData.amount || '€0.00',
          status: orderData.status || 'Pending',
          paymentStatus: orderData.paymentStatus || 'Pending',
          items: orderData.items || 1,
          location: orderData.location || 'Unknown',
          paymentMethod: orderData.paymentMethod || 'Bank Transfer'
        };

        // Add to orders list
        setOrders(prevOrders => [formattedOrder, ...prevOrders]);
        setAllOrders(prevOrders => [formattedOrder, ...prevOrders]);

        console.log('Added order to state:', formattedOrder);

        try {
          // Refresh data
          await fetchOrders();
          await fetchOrderStats();

          // Update the last updated timestamp
          setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        } catch (fetchErr) {
          console.error("Error refreshing data after order creation:", fetchErr);
        }
      } else {
        // If orderData doesn't have success flag, try to refresh data anyway
        await loadAllData();
      }

    } catch (err) {
      console.error("Error handling order creation:", err);
      alert('Failed to process order: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Function to update order status
const handleUpdateStatus = async (orderId, newStatus) => {
  try {
    setLoading(true);

    // Determine if this is an upgrade order based on the selectedOrder data
    const isUpgradeOrder = selectedOrder && selectedOrder.orderType === 'upgrade';
    
    // Clean order ID (remove prefixes like 'ORD-' or 'UPG-')
    const cleanId = orderId.toString().replace(/\D/g, '');

    // Choose the appropriate endpoint
    const endpoint = isUpgradeOrder ? 'update_upgrade_order_status' : 'update_order_status';

    const response = await fetch(`${API_URL}/api_admin_orders.php?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: localStorage.getItem('admin_token'),
        id: cleanId,
        status: newStatus
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to update order status');
    }

    // Update the appropriate state based on order type
    if (isUpgradeOrder) {
      // Update upgrade orders state
      setUpgradeOrders(prevOrders => prevOrders.map(order =>
        order.id.toString() === cleanId ? { ...order, status: newStatus } : order
      ));

      setAllUpgradeOrders(prevOrders => prevOrders.map(order =>
        order.id.toString() === cleanId ? { ...order, status: newStatus } : order
      ));
    } else {
      // Update regular orders state
      setOrders(prevOrders => prevOrders.map(order =>
        order.id === orderId ? { ...order, status: newStatus } : order
      ));

      setAllOrders(prevOrders => prevOrders.map(order =>
        order.id === orderId ? { ...order, status: newStatus } : order
      ));
    }

    // If we have a selected order, update it too
    if (selectedOrder && (
      selectedOrder.id === orderId || 
      selectedOrder.id === cleanId ||
      selectedOrder.id === `UPG-${cleanId}` ||
      selectedOrder.id === `ORD-${cleanId}`
    )) {
      setSelectedOrder({ ...selectedOrder, status: newStatus });
    }

    // Show success message
    alert(`${isUpgradeOrder ? 'Upgrade' : 'Order'} status updated to ${newStatus} successfully!`);

    // Refresh data to get updated list
    await loadAllData();

  } catch (err) {
    console.error("Error updating order status:", err);
    alert('Failed to update order status: ' + err.message);
  } finally {
    setLoading(false);
  }
};

  // Function to get order details
const handleOrderClick = async (order) => {
  try {
    setLoading(true);

    console.log('Order clicked:', order);
    console.log('Active tab:', activeTab);

    // Check if this is an upgrade order based on activeTab
    const isUpgradeOrder = activeTab === 'upgrades';
    
    // Clean order ID (remove any prefixes and get just the number)
    const orderId = order.id.toString().replace(/\D/g, '');
    
    console.log('Processing order ID:', orderId, 'as', isUpgradeOrder ? 'upgrade' : 'regular', 'order');

    // Choose the appropriate endpoint based on order type
    const endpoint = isUpgradeOrder ? 'get_upgrade_order_details' : 'get_order_details';
    const apiUrl = `${API_URL}/api_admin_orders.php?f=${endpoint}`;

    console.log('Calling endpoint:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: localStorage.getItem('admin_token'),
        id: orderId
      })
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('HTTP error response:', errorText);
      throw new Error(`HTTP error ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('Response data:', data);

    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch order details');
    }

    // Add order type information to the data so the modal knows how to display it
    data.orderType = isUpgradeOrder ? 'upgrade' : 'regular';
    
    // For upgrade orders, ensure we have the proper ID format for display
    if (isUpgradeOrder && !data.id.startsWith('UPG-')) {
      data.id = 'UPG-' + orderId;
    }

    console.log('Setting selected order:', data);

    // Set selected order with full details
    setSelectedOrder(data);
    
  } catch (err) {
    console.error("Error fetching order details:", err);
    
    // More detailed error message
    let errorMessage = "Failed to fetch order details: " + err.message;
    
    if (err.message.includes('not found')) {
      const orderType = activeTab === 'upgrades' ? 'upgrade order' : 'order';
      errorMessage = `${orderType} not found. Please check if the ${orderType} exists in the database.`;
    }
    
    alert(errorMessage);
    
    // Close the modal instead of using fallback data
    setSelectedOrder(null);
  } finally {
    setLoading(false);
  }
};



  // Function to handle clicking on a service - using the same approach as ServicesTab.js
  const handleServiceClick = async (item, itemIndex) => {
    setLoading(true);

    console.log('Item for service modal:', item);

    try {
      // Simplified ID determination - focus on getting the orders_items.id
      let serviceId = null;

      // If item has item_id (which would be orders_items.id), use that
      if (item && item.item_id) {
        console.log('Using item.item_id as service ID:', item.item_id);
        serviceId = item.item_id;
      }
      // Otherwise if item has id directly, use that
      else if (item && item.id) {
        console.log('Using item.id as service ID:', item.id);
        serviceId = item.id;
      }
      // Check for any other possible ID fields
      else if (item && item.orders_items_id) {
        console.log('Using item.orders_items_id as service ID:', item.orders_items_id);
        serviceId = item.orders_items_id;
      }
      else if (item && item.database_id) {
        console.log('Using item.database_id as service ID:', item.database_id);
        serviceId = item.database_id;
      }

      if (serviceId) {
        setSelectedServiceId(serviceId);
        setServiceModalOpen(true);
      } else {
        throw new Error('Could not determine service ID from item - no valid ID field found');
      }
    } catch (err) {
      console.error('Error setting service ID:', err);
      alert('Could not set service details: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Function to close the service modal
  const handleCloseServiceModal = () => {
    setServiceModalOpen(false);
    setSelectedServiceId(null);
  };

  // UI handlers
  // Use the toggleSidebar prop passed from the parent component
  // instead of defining a local function that doesn't update the parent state

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
  };

  const handleLocationFilter = (location) => {
    setSelectedLocation(location);
  };

  const handlePaymentStatusFilter = (status) => {
    setSelectedPaymentStatus(status);
  };

  // Handle statistics card click for filtering
  const handleStatCardClick = (statTitle) => {
    // Map statistics card titles to their corresponding status values
    const statusMapping = {
      'Total Orders': 'All',
      'Completed': 'Completed',
      'Pending': 'Pending',
      'Cancelled': 'Cancelled'
    };

    const targetStatus = statusMapping[statTitle];
    if (targetStatus) {
      setSelectedStatus(targetStatus);
      // Reset other filters when clicking on a stat card for cleaner filtering
      setSearchQuery('');
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleRefreshData = () => {
    loadAllData();
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  const closeOrderDetails = () => {
    setSelectedOrder(null);
  };

  // Function to handle viewing an invoice
  const handleViewInvoice = () => {
    if (!selectedOrder || !selectedOrder.invoice) {
      alert('No invoice available for this order');
      return;
    }

    // Log the selected order to see its structure
    console.log('Selected order for invoice view:', selectedOrder);

    // Format the items for the InvoiceModal
    const formattedItems = [];

    if (selectedOrder.items && Array.isArray(selectedOrder.items)) {
      // Map the order items to the format expected by InvoiceModal
      formattedItems.push(...selectedOrder.items.map(item => {
        // Extract numeric values from price strings
        const priceStr = item.price?.toString() || '0';
        const unitPrice = parseFloat(priceStr.replace(/[^0-9.]/g, '')) || 0;

        const totalStr = item.total?.toString() || priceStr;
        const total = parseFloat(totalStr.replace(/[^0-9.]/g, '')) || unitPrice * (item.quantity || 1);

        return {
          description: item.name || '',
          quantity: parseInt(item.quantity) || 1,
          unitPrice: unitPrice.toFixed(2),
          total: total.toFixed(2)
        };
      }));
    }

    // If no items were found, add a placeholder item
    if (formattedItems.length === 0) {
      formattedItems.push({
        description: 'Server Order',
        quantity: 1,
        unitPrice: '0.00',
        total: '0.00'
      });
    }

    // Calculate subtotal, tax, and total from the formatted items
    const subtotal = formattedItems.reduce((sum, item) => sum + parseFloat(item.total), 0);
    const vatRate = 0.19; // Default VAT rate (19%)
    const tax = subtotal * vatRate;
    const total = subtotal + tax;

    // Format the invoice data for the InvoiceModal
    const invoiceData = {
      id: selectedOrder.invoice.proformaNumber || selectedOrder.invoice.invoiceNumber || 'N/A',
      isProforma: selectedOrder.invoice.type === 'proforma',
      client: selectedOrder.customerName || 'Customer',
      clientId: selectedOrder.customerId || '',
      clientEmail: selectedOrder.customerEmail || '',
      date: selectedOrder.invoice.date || new Date().toISOString().split('T')[0],
      dueDate: selectedOrder.invoice.dueDate || new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0],
      status: selectedOrder.invoice.status || 'Pending',
      paymentMethod: selectedOrder.invoice.paymentMethod || 'Bank Transfer',
      items: formattedItems,
      amount: selectedOrder.invoice.amount?.replace(/[^0-9.]/g, '') || total.toFixed(2),
      subtotal: selectedOrder.invoice.subtotal || `€${subtotal.toFixed(2)}`,
      tax: selectedOrder.invoice.tax || `€${tax.toFixed(2)}`,
      total: selectedOrder.invoice.amount || `€${total.toFixed(2)}`,
      vatRate: vatRate,
      // If there's a description, include it in the notes
      notes: selectedOrder.invoice.description ?
        (selectedOrder.invoice.description + (selectedOrder.invoice.notes ? '\n\n' + selectedOrder.invoice.notes : '')) :
        (selectedOrder.invoice.notes || '')
    };

    console.log('Formatted invoice data:', invoiceData);

    // First close any open modal
    setViewModalOpen(false);
    setViewingInvoice(null);

    // Open the view modal with a slight delay to ensure clean rendering
    setTimeout(() => {
      setViewingInvoice(invoiceData);
      setViewModalOpen(true);
    }, 100);
  };

  // Function to close the invoice modal
  const closeInvoiceModal = () => {
    setInvoiceModalOpen(false);
    setSelectedInvoice(null);
  };

  // Enhanced close view modal function
  const closeViewModal = () => {
    setViewModalOpen(false);
    // Clear viewing invoice with a delay to avoid UI flashing
    setTimeout(() => {
      setViewingInvoice(null);
    }, 100);
  };

  // Handle editing invoice from view
  const handleEditFromView = (invoice) => {
    // First close the view modal
    closeViewModal();

    // Wait for the modal to fully close
    setTimeout(() => {
      // Open edit modal with the invoice
      openEditModal(invoice);
    }, 200);
  };

  // Modal control functions for edit/create
  const openEditModal = (invoice) => {
    setSelectedInvoice(invoice);
    setEditModalMode('edit');
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setSelectedInvoice(null);
  };

  // Handle print invoice
  const handlePrintInvoice = (invoice) => {
    console.log('Printing invoice:', invoice);
    // Implement printing logic
    window.print();
  };

  // Handle download invoice
  const handleDownloadInvoice = (invoice) => {
    console.log('Downloading invoice:', invoice);
    // Implement download logic
    alert('Download functionality will be implemented soon.');
  };

  // Handle send invoice
  const handleSendInvoice = (invoice) => {
    console.log('Sending invoice:', invoice);
    // Implement send logic
    alert('Send functionality will be implemented soon.');
  };

  // Handle save invoice
  const handleSaveInvoice = async (invoiceData) => {
    try {
      console.log('Saving invoice:', invoiceData);

      if (editModalMode === 'edit') {
        // Update existing invoice
        if (invoiceData.items.some(item => !item.description || !item.unitPrice)) {
          alert('Please fill in all required fields');
          return;
        }

        // Get the authentication token
        const token = localStorage.getItem('admin_token');
        if (!token) {
          throw new Error('Authentication token not found. Please log in again.');
        }

        // Updated payload to match the PHP endpoint requirements
        const contentPayload = {
          token: token,
          id: invoiceData.id,
          notes: invoiceData.notes || '',
          isProforma: invoiceData.isProforma ? 1 : 0, // Include proforma flag
          items: invoiceData.items.map(item => ({
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.total
          })),
          date: invoiceData.date,
          dueDate: invoiceData.dueDate,
          payment_method: invoiceData.paymentMethod
        };

        // First, update the invoice content using update_invoice_content endpoint
        const contentResponse = await fetch(`${API_URL}/api_admin_invoices.php?f=update_invoice_content`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(contentPayload)
        });

        if (!contentResponse.ok) {
          const errorText = await contentResponse.text();
          console.error("Error response from API:", errorText);
          throw new Error(`Failed to update invoice content: ${errorText}`);
        }

        let contentResult;
        try {
          contentResult = await contentResponse.json();
        } catch (parseError) {
          console.error("Failed to parse response:", parseError);
          throw new Error("Invalid response from server");
        }

        if (!contentResult.success) {
          throw new Error(contentResult.error || 'Failed to update invoice content');
        }

        // Close the edit modal
        closeEditModal();

        // Show success message
        alert('Invoice updated successfully!');

        // Refresh data
        loadAllData();
      }
    } catch (err) {
      console.error("Error saving invoice:", err);
      alert('Failed to save invoice: ' + err.message);
    }
  };

  // Render functions for UI components
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Completed': 'bg-green-100 text-green-800',
      'Processing': 'bg-blue-100 text-blue-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Cancelled': 'bg-red-100 text-red-800',
      'Disputed': 'bg-purple-100 text-purple-800'
    };

    const icons = {
      'Completed': <CheckCircle className="w-4 h-4 mr-1" />,
      'Processing': <Clock className="w-4 h-4 mr-1" />,
      'Pending': <AlertTriangle className="w-4 h-4 mr-1" />,
      'Cancelled': <XCircle className="w-4 h-4 mr-1" />,
      'Disputed': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  const renderPaymentStatusBadge = (status) => {
    const badgeClasses = {
      'Paid': 'bg-green-100 text-green-800',
      'Pending': 'bg-yellow-100 text-yellow-800',
      'Refunded': 'bg-blue-100 text-blue-800',
      'On Hold': 'bg-gray-100 text-gray-800'
    };

    const icons = {
      'Paid': <CheckCircle className="w-4 h-4 mr-1" />,
      'Pending': <Clock className="w-4 h-4 mr-1" />,
      'Refunded': <ArrowUp className="w-4 h-4 mr-1" />,
      'On Hold': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Get current data based on active tab
  const getCurrentOrders = () => {
    return activeTab === 'upgrades' ? upgradeOrders : orders;
  };

  // Filter orders based on search query, selected status, and location
  const filteredOrders = getCurrentOrders().filter(order => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (order.hostname && order.hostname.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = selectedStatus === 'All' || order.status === selectedStatus;
    const matchesLocation = selectedLocation === 'All Locations' || order.location === selectedLocation;
    const matchesPaymentStatus = selectedPaymentStatus === 'All' || order.paymentStatus === selectedPaymentStatus;

    return matchesSearch && matchesStatus && matchesLocation && matchesPaymentStatus;
  });

  // Sort filtered orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    let comparison = 0;

    if (sortField === 'id') {
      comparison = a.id.localeCompare(b.id);
    } else if (sortField === 'customerName') {
      comparison = a.customerName.localeCompare(b.customerName);
    } else if (sortField === 'date') {
      comparison = new Date(a.date) - new Date(b.date);
    } else if (sortField === 'amount') {
      comparison = parseFloat(a.amount.replace('€', '').replace(',', '')) -
                  parseFloat(b.amount.replace('€', '').replace(',', ''));
    } else if (sortField === 'status') {
      comparison = a.status.localeCompare(b.status);
    } else if (sortField === 'location') {
      comparison = a.location.localeCompare(b.location);
    } else if (sortField === 'paymentStatus') {
      comparison = a.paymentStatus.localeCompare(b.paymentStatus);
    }

    return sortDirection === 'asc' ? comparison : -comparison;
  });

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar sidebarCollapsed={sidebarCollapsed} activeMenu="Orders" navigateTo={navigateTo} toggleSidebar={toggleSidebar} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Orders Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Order Management</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                onClick={() => setNewOrderModalOpen(true)}
              >
                <Plus className="w-4 h-4 mr-1" />
                New Order
              </button>
            </div>
          </div>

          {/* Order Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {loading ? (
              Array(4).fill(0).map((_, index) => (
                <div
                  key={index}
                  className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card animate-pulse"
                >
                  <div className="w-full">
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))
            ) : (
              orderStats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white p-6 shadow-sm flex items-center justify-between border rounded metric-card cursor-pointer hover:shadow-md transition-shadow duration-200"
                  onClick={() => handleStatCardClick(stat.title)}
                >
                  <div>
                    <div className="text-sm text-gray-700">{stat.title}</div>
                    <div className="text-2xl font-bold mt-1">{stat.value}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      <span className={stat.change.startsWith('+') ? 'text-green-500' : 'text-red-500'}>
                        {stat.change}
                      </span> {stat.period}
                    </div>
                  </div>
                  <div className={`card-custom-icon ${stat.icon_class}`}>
                    {stat.title === 'Total Orders' ? (
                      <ShoppingCart className="text-indigo-700" size={40} strokeWidth={2} />
                    ) : stat.title === 'Completed' ? (
                      <CheckCircle className="text-success" size={40} strokeWidth={2} />
                    ) : stat.title === 'Pending' ? (
                      <Clock className="text-warning" size={40} strokeWidth={2} />
                    ) : (
                      <XCircle className="text-danger" size={40} strokeWidth={2} />
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Tab Navigation */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="border-b">
              <nav className="flex space-x-8 px-4">
                <button
                  onClick={() => setActiveTab('orders')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'orders'
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Regular Orders ({orders.length})
                </button>
                <button
                  onClick={() => setActiveTab('upgrades')}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'upgrades'
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Upgrade Orders ({upgradeOrders.length})
                </button>
              </nav>
            </div>

            {/* Orders Filter and Search */}
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex space-x-2">
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedStatus}
                    onChange={(e) => handleStatusFilter(e.target.value)}
                  >
                    <option value="All">All Status</option>
                    <option value="Completed">Completed</option>
                    <option value="Processing">Processing</option>
                    <option value="Pending">Pending</option>
                    <option value="Cancelled">Cancelled</option>
                    <option value="Disputed">Disputed</option>
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedLocation}
                    onChange={(e) => handleLocationFilter(e.target.value)}
                  >
                    {uniqueLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedPaymentStatus}
                    onChange={(e) => handlePaymentStatusFilter(e.target.value)}
                  >
                    {uniquePaymentStatuses.map(status => (
                      <option key={status} value={status}>{status === 'All' ? 'All Payment Status' : status}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search orders..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Orders Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-gray-500 text-xs border-b">
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('id')}>
                      {activeTab === 'upgrades' ? 'UPGRADE ID' : 'ORDER ID'} {getSortIcon('id')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('customerName')}>
                      CUSTOMER {getSortIcon('customerName')}
                    </th>
                    {activeTab === 'upgrades' && (
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('hostname')}>
                        HOSTNAME {getSortIcon('hostname')}
                      </th>
                    )}
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('date')}>
                      DATE {getSortIcon('date')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('amount')}>
                      AMOUNT {getSortIcon('amount')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                      STATUS {getSortIcon('status')}
                    </th>
                    {activeTab === 'orders' && (
                      <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('paymentStatus')}>
                        PAYMENT {getSortIcon('paymentStatus')}
                      </th>
                    )}
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('location')}>
                      LOCATION {getSortIcon('location')}
                    </th>
                    {activeTab === 'orders' && (
                      <th className="p-4 text-left font-medium">ITEMS</th>
                    )}
                    {activeTab === 'upgrades' && (
                      <th className="p-4 text-left font-medium">UPGRADE TYPE</th>
                    )}

                  </tr>
                </thead>
                <tbody>
                  {loading && getCurrentOrders().length === 0 ? (
                    // Loading skeleton
                    Array(5).fill(0).map((_, index) => (
                      <tr key={index} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></td>
                        {activeTab === 'upgrades' && (
                          <td className="p-4"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                        )}
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                        {activeTab === 'orders' && (
                          <td className="p-4"><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></td>
                        )}
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></td>
                        <td className="p-4"><div className="h-4 bg-gray-200 rounded w-8 animate-pulse"></div></td>
                      </tr>
                    ))
                  ) : sortedOrders.length > 0 ? (
                    sortedOrders.map((order, index) => (
                      <tr
                        key={order.id}
                        className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-50 task-row cursor-pointer`}
                        onClick={() => handleOrderClick(order)}
                      >
                        <td className="p-4 font-medium text-indigo-700">{order.id}</td>
                        <td className="p-4 text-gray-700">{order.customerName}</td>
                        {activeTab === 'upgrades' && (
                          <td className="p-4 text-gray-700">{order.hostname || 'N/A'}</td>
                        )}
                        <td className="p-4 text-gray-700">{new Date(order.date).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}</td>
                        <td className="p-4 font-medium">{order.amount}</td>
                        <td className="p-4">{renderStatusBadge(order.status)}</td>
                        {activeTab === 'orders' && (
                          <td className="p-4">{renderPaymentStatusBadge(order.paymentStatus)}</td>
                        )}
                        <td className="p-4 text-gray-700">{order.location}</td>
                        {activeTab === 'orders' && (
                          <td className="p-4 text-gray-700">{order.items}</td>
                        )}
                        {activeTab === 'upgrades' && (
                          <td className="p-4 text-gray-700">{order.upgrade_type || order.type || 'Upgrade'}</td>
                        )}

                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={activeTab === 'upgrades' ? "7" : "8"} className="p-4 text-center text-gray-500">
                        No {activeTab === 'upgrades' ? 'upgrade orders' : 'orders'} found matching your criteria
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="p-4 border-t flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Showing {sortedOrders.length} of {getCurrentOrders().length} {activeTab === 'upgrades' ? 'upgrade orders' : 'orders'}
              </div>
              <div className="flex space-x-1">
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Previous</button>
                <button className="px-3 py-1 border rounded text-sm bg-indigo-700 text-white">1</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700">2</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-700">3</button>
                <button className="px-3 py-1 border rounded text-sm bg-white text-gray-500">Next</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Details Modal */}

{selectedOrder && (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg shadow-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto">
      <div className="p-6 border-b flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-800">
          {selectedOrder.orderType === 'upgrade' ? 'Upgrade Order Details' : 'Order Details'}
        </h2>
        <button
          onClick={closeOrderDetails}
          className="text-gray-500 hover:text-gray-700"
        >
          <XCircle className="w-5 h-5" />
        </button>
      </div>
      <div className="p-6 space-y-6">
        {/* Order Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-4">
          <div>
            <div className="text-2xl font-bold text-gray-800">{selectedOrder.id}</div>
            <div className="text-sm text-gray-500">
              {selectedOrder.orderType === 'upgrade' ? 'Upgrade placed' : 'Order placed'} on {' '}
              {new Date(selectedOrder.date).toLocaleDateString('en-GB', { 
                day: '2-digit', 
                month: '2-digit', 
                year: 'numeric' 
              })}
            </div>
            {selectedOrder.orderType === 'upgrade' && selectedOrder.hostname && (
              <div className="text-sm text-blue-600 mt-1">
                Service: {selectedOrder.hostname}
              </div>
            )}
          </div>
          <div className="mt-2 md:mt-0 flex gap-2">
            {renderStatusBadge(selectedOrder.status)}
            {/* Status Update Dropdown */}
            <div className="relative ml-2">
              <select
                className="pl-3 pr-8 py-1 border rounded-md text-xs text-gray-800 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-200"
                value={selectedOrder.status}
                onChange={(e) => handleUpdateStatus(selectedOrder.id, e.target.value)}
              >
                <option value="Pending">Pending</option>
                <option value="Processing">Processing</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Disputed">Disputed</option>
              </select>
            </div>
          </div>
        </div>

        {/* Customer and Configuration Information Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div>
            <h3 className="text-lg font-bold mb-2">Customer Information</h3>
            <div className="bg-gray-50 rounded-md p-4 h-full">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Customer Name</div>
                  <div className="font-medium">{selectedOrder.customerName}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Location</div>
                  <div className="font-medium">{selectedOrder.location}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Payment Method</div>
                  <div className="font-medium">{selectedOrder.paymentMethod}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">
                    {selectedOrder.orderType === 'upgrade' ? 'Upgrade Cost' : 'Order Total'}
                  </div>
                  <div className="font-medium">{selectedOrder.amount}</div>
                </div>
                {selectedOrder.orderType === 'upgrade' && selectedOrder.parent_order_id && (
                  <div className="col-span-2">
                    <div className="text-sm text-gray-500">Parent Order</div>
                    <div className="font-medium">#{selectedOrder.parent_order_id}</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Upgrade Configuration or Invoice Information */}
{selectedOrder.orderType === 'upgrade' ? (
  <div>
    <h3 className="text-lg font-bold mb-2">Upgrade Configuration</h3>
    <div className="bg-gray-50 rounded-md p-4 h-full">
      <UpgradeConfigurationDisplay upgradeDetails={selectedOrder.upgrade_details} />
    </div>
  </div>
) : (
            // Regular order invoice information
            selectedOrder.invoice ? (
              <div>
                <h3 className="text-lg font-bold mb-2">Invoice Information</h3>
                <div className="bg-gray-50 rounded-md p-4 h-full">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">Invoice Number</div>
                      <div className="font-medium">
                        {selectedOrder.invoice.type === 'proforma'
                          ? `Proforma #${selectedOrder.invoice.proformaNumber}`
                          : `Invoice #${selectedOrder.invoice.invoiceNumber}`}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Invoice Date</div>
                      <div className="font-medium">{new Date(selectedOrder.invoice.date).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Due Date</div>
                      <div className="font-medium">{new Date(selectedOrder.invoice.dueDate).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' })}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Invoice Status</div>
                      <div className="mt-1">{renderPaymentStatusBadge(selectedOrder.invoice.status)}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Price with VAT</div>
                      <div className="font-medium">{selectedOrder.invoice.amount}</div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-bold mb-2">Invoice Information</h3>
                <div className="bg-gray-50 rounded-md p-4 h-full flex items-center justify-center">
                  <div className="text-gray-500 italic">No invoice information available</div>
                </div>
              </div>
            )
          )}
        </div>

        {/* Order Items */}
        <div>
          <h3 className="text-lg font-bold mb-2">
            {selectedOrder.orderType === 'upgrade' ? 'Upgrade Details' : 'Order Items'}
          </h3>
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr className="text-left text-gray-500 text-xs">
                  <th className="p-3 font-medium w-1/3">
                    {selectedOrder.orderType === 'upgrade' ? 'UPGRADE' : 'ITEM'}
                  </th>
                  <th className="p-3 font-medium w-1/6">STATUS</th>
                  <th className="p-3 font-medium w-1/6 text-right">
                    {selectedOrder.orderType === 'upgrade' ? 'UPGRADE COST' : 'UNIT PRICE'}
                  </th>
                  <th className="p-3 font-medium w-1/6 text-right">TOTAL</th>
                </tr>
              </thead>
              <tbody>
                {selectedOrder.items && Array.isArray(selectedOrder.items) && selectedOrder.items.length > 0 ? (
                  selectedOrder.items.map((item, i) => (
                    <tr
                      key={i}
                      className="border-t hover:bg-gray-50 cursor-pointer"
                      onClick={() => {
                        if (selectedOrder.orderType === 'upgrade') {
                          // For upgrades, show upgrade details instead of service details
                          console.log('Upgrade item clicked:', item);
                        } else {
                          handleServiceClick(item, i);
                        }
                      }}
                    >
                      <td className="p-3">
                        <div className="flex items-center">
                          <Package className="w-8 h-8 text-gray-400 mr-3" />
                          <div>
                            <div className="font-medium" title={item.name}>
                              {selectedOrder.orderType === 'upgrade' 
                                ? `${selectedOrder.upgrade_details?.upgrade_type || 'Service Upgrade'}`
                                : (item.name && item.name.includes('|')
                                    ? (() => {
                                        const parts = item.name.split('|');
                                        const firstPart = parts[0].trim();
                                        if (firstPart.includes(':')) {
                                          return firstPart.split(':')[1].trim();
                                        }
                                        return firstPart;
                                      })()
                                    : (item.cpu || item.name || 'Service Item'))}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {selectedOrder.orderType === 'upgrade' 
                                ? `Hostname: ${selectedOrder.hostname || 'N/A'}`
                                : `Item ID: #${item.item_id || item.id || item.orders_items_id || 'N/A'}`}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-3">
                        <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                          {item.status || selectedOrder.status}
                        </span>
                      </td>
                      <td className="p-3 text-right">{item.price}</td>
                      <td className="p-3 text-right font-medium">{item.total || item.price}</td>
                    </tr>
                  ))
                ) : (
                  <tr className="border-t">
                    <td colSpan="4" className="p-4 text-center text-gray-500">
                      No {selectedOrder.orderType === 'upgrade' ? 'upgrade details' : 'items'} available
                    </td>
                  </tr>
                )}
              </tbody>
              <tfoot>
                <tr className="border-t bg-gray-50">
                  <td className="p-3"></td>
                  <td className="p-3"></td>
                  <td className="p-3 text-right font-medium">Total:</td>
                  <td className="p-3 text-right font-bold">{selectedOrder.amount}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* Order Actions */}
        <div className="flex flex-wrap gap-3 justify-end pt-4 border-t">
          {selectedOrder.orderType !== 'upgrade' && (
            <button
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
              onClick={handleViewInvoice}
              disabled={!selectedOrder.invoice}
            >
              <FileText className="w-4 h-4 mr-1" />
              View Invoice
            </button>
          )}

          <button className="px-4 py-2 border border-red-300 rounded-md text-red-700 flex items-center text-sm hover:bg-red-50">
            <Trash2 className="w-4 h-4 mr-1" />
            {selectedOrder.orderType === 'upgrade' ? 'Cancel Upgrade' : 'Cancel Order'}
          </button>
        </div>
      </div>
    </div>
  </div>
)}

      {/* New Order Modal */}
      <NewOrderModal
        isOpen={newOrderModalOpen}
        onClose={() => setNewOrderModalOpen(false)}
        onSave={handleCreateOrder}
        searchResults={searchResults}
        isSearching={isSearching}
        onClientSearch={handleClientSearch}
      />

      {/* Invoice Modal - Regular invoices */}
      {invoiceModalOpen && selectedInvoice && !viewModalOpen && (
        <InvoiceModal
          isOpen={invoiceModalOpen}
          mode="view"
          invoice={selectedInvoice}
          onClose={closeInvoiceModal}
        />
      )}

      {/* Edit Invoice Modal */}
      <InvoiceModal
        isOpen={editModalOpen}
        mode={editModalMode}
        invoice={selectedInvoice}
        onClose={closeEditModal}
        onSave={handleSaveInvoice}
        onUpdateStatus={handleUpdateStatus}
        onSendInvoice={handleSendInvoice}
        onPrint={handlePrintInvoice}
        onDownload={handleDownloadInvoice}
        searchResults={searchResults}
        isSearching={isSearching}
        onClientSearch={handleClientSearch}
        statuses={['Paid', 'Pending', 'Overdue', 'Disputed', 'Draft']}
        paymentMethods={['Bank Transfer', 'Credit Card', 'PayPal', 'Cash', 'Check']}
      />

      {/* Invoice View - For invoices opened from order modal */}
      {viewModalOpen && viewingInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            {/* Custom header with close button */}
            <div className="bg-white p-6 border-b flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {viewingInvoice.isProforma ? 'Proforma Invoice' : 'Invoice'} #{viewingInvoice.id}
                </h2>
                <div className="flex flex-wrap gap-2">
                  {/* Type badge */}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${viewingInvoice.isProforma ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                    <FileText className="w-4 h-4 mr-1" />
                    {viewingInvoice.isProforma ? 'Proforma' : 'Invoice'}
                  </span>

                  {/* Invoice number badge */}
                  <span className="px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit bg-gray-100 text-gray-800">
                    <File className="w-4 h-4 mr-1" />
                    #{viewingInvoice.id}
                  </span>

                  {/* Status badge */}
                  {viewingInvoice.status && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${viewingInvoice.status === 'Paid' ? 'bg-green-100 text-green-800' : viewingInvoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : viewingInvoice.status === 'Overdue' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>
                      {viewingInvoice.status === 'Paid' ? <CheckCircle className="w-4 h-4 mr-1" /> :
                       viewingInvoice.status === 'Pending' ? <Clock className="w-4 h-4 mr-1" /> :
                       viewingInvoice.status === 'Overdue' ? <AlertCircle className="w-4 h-4 mr-1" /> :
                       <File className="w-4 h-4 mr-1" />}
                      {viewingInvoice.status}
                    </span>
                  )}
                </div>
              </div>
              <button
                onClick={closeViewModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            {/* InvoiceView will fetch its own data */}
            <InvoiceView
              invoice={viewingInvoice}
              onEdit={handleEditFromView}
              onUpdateStatus={handleUpdateStatus}
              onSend={handleSendInvoice}
              onPrint={handlePrintInvoice}
              onDownload={handleDownloadInvoice}
              className="border-t-0 rounded-t-none"
              hideHeader={true}
              defaultActiveTab="details"
            />
          </div>
        </div>
      )}

      {/* Service Detail Modal */}
      <ServiceDetailModal
        isOpen={serviceModalOpen}
        onClose={handleCloseServiceModal}
        serviceId={selectedServiceId}
        API_BASE_URL={API_URL}
      />
    </div>
  );
};

export default OrdersPage;